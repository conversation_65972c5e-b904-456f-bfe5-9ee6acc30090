<template>
  <div class="rem-example">
    <h1 class="title">PostCSS rem 自适应方案示例</h1>
    
    <!-- 使用 UnoCSS rem 工具类 -->
    <div class="uno-section">
      <h2 class="text-rem-20 m-rem-16">UnoCSS rem 工具类示例</h2>
      <div class="p-rem-16 bg-blue-100 rounded-rem-8">
        <p class="text-rem-14 mb-rem-8">这是使用 UnoCSS rem 工具类的文本</p>
        <button class="px-rem-16 py-rem-8 bg-blue-500 text-white rounded-rem-4 text-rem-14">
          按钮示例
        </button>
      </div>
    </div>

    <!-- 使用 SCSS 混合宏 -->
    <div class="scss-section">
      <h2 class="scss-title">SCSS 混合宏示例</h2>
      <div class="scss-card">
        <p class="scss-text">这是使用 SCSS 混合宏的文本</p>
        <button class="scss-button">SCSS 按钮</button>
      </div>
    </div>

    <!-- 普通 CSS（会被 PostCSS 自动转换） -->
    <div class="css-section">
      <h2 class="css-title">普通 CSS 自动转换示例</h2>
      <div class="css-card">
        <p class="css-text">这些 px 值会被 PostCSS 自动转换为 rem</p>
        <button class="css-button">CSS 按钮</button>
      </div>
    </div>

    <!-- 不转换的示例 -->
    <div class="no-rem no-convert-section">
      <h2 class="no-rem-title">不转换示例（使用 no-rem 类）</h2>
      <div class="no-rem-card">
        <p class="no-rem-text">这些样式不会被转换为 rem</p>
        <button class="no-rem-button">不转换按钮</button>
      </div>
    </div>

    <!-- 响应式示例 -->
    <div class="responsive-section">
      <h2 class="responsive-title">响应式示例</h2>
      <div class="responsive-card">
        <p class="responsive-text">这个文本在不同屏幕尺寸下有不同的样式</p>
      </div>
    </div>

    <!-- 当前 rem 信息显示 -->
    <div class="info-section">
      <h2 class="info-title">当前 rem 信息</h2>
      <div class="info-card">
        <p>当前根字体大小: {{ currentRemBase }}px</p>
        <p>当前缩放比例: {{ currentScale }}</p>
        <p>屏幕宽度: {{ screenWidth }}px</p>
        <button @click="updateRemInfo" class="update-button">更新信息</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import remAdapter from '@/utils/rem.js';

// 响应式数据
const currentRemBase = ref(16);
const currentScale = ref(1);
const screenWidth = ref(window.innerWidth);

// 更新 rem 信息
const updateRemInfo = () => {
  currentRemBase.value = remAdapter.getRemBase();
  currentScale.value = remAdapter.getScaleRatio();
  screenWidth.value = window.innerWidth;
};

// 监听 rem 更新事件
const handleRemUpdate = (event) => {
  currentRemBase.value = event.detail.fontSize;
  currentScale.value = event.detail.ratio;
  screenWidth.value = event.detail.width;
};

onMounted(() => {
  updateRemInfo();
  window.addEventListener('remUpdated', handleRemUpdate);
});

onUnmounted(() => {
  window.removeEventListener('remUpdated', handleRemUpdate);
});
</script>

<style scoped lang="scss">
.rem-example {
  padding: 20px; // 这个会被转换为 rem
  max-width: 1200px; // 这个会被转换为 rem
  margin: 0 auto; // margin 值为 0 不会被转换
}

.title {
  @include font-size(32);
  @include margin-bottom(24);
  color: #333;
  text-align: center;
}

// UnoCSS 部分样式
.uno-section {
  @include margin-bottom(32);
}

// SCSS 混合宏示例
.scss-section {
  @include margin-bottom(32);
}

.scss-title {
  @include font-size(20);
  @include margin-bottom(16);
  color: #666;
}

.scss-card {
  @include padding(16);
  background-color: #f0f9ff;
  @include border-radius(8);
  border: 1px solid #e0e7ff; // 1px 边框不会被转换（小于 minPixelValue）
}

.scss-text {
  @include font-size(14);
  @include margin-bottom(12);
  color: #374151;
}

.scss-button {
  @include padding(8, 16);
  @include font-size(14);
  background-color: #3b82f6;
  color: white;
  border: none;
  @include border-radius(4);
  cursor: pointer;
  
  &:hover {
    background-color: #2563eb;
  }
}

// 普通 CSS 自动转换示例
.css-section {
  margin-bottom: 32px; // 会被转换为 rem
}

.css-title {
  font-size: 20px; // 会被转换为 rem
  margin-bottom: 16px; // 会被转换为 rem
  color: #666;
}

.css-card {
  padding: 16px; // 会被转换为 rem
  background-color: #f0fdf4;
  border-radius: 8px; // 会被转换为 rem
  border: 1px solid #dcfce7; // 1px 不会被转换
}

.css-text {
  font-size: 14px; // 会被转换为 rem
  margin-bottom: 12px; // 会被转换为 rem
  color: #374151;
}

.css-button {
  padding: 8px 16px; // 会被转换为 rem
  font-size: 14px; // 会被转换为 rem
  background-color: #10b981;
  color: white;
  border: none;
  border-radius: 4px; // 会被转换为 rem
  cursor: pointer;
  
  &:hover {
    background-color: #059669;
  }
}

// 不转换示例（使用 no-rem 类）
.no-convert-section {
  margin-bottom: 32px; // 这个会被转换，因为父元素没有 no-rem 类
}

.no-rem-title {
  font-size: 20px; // 不会被转换
  margin-bottom: 16px; // 不会被转换
  color: #666;
}

.no-rem-card {
  padding: 16px; // 不会被转换
  background-color: #fef3c7;
  border-radius: 8px; // 不会被转换
  border: 1px solid #fde68a;
}

.no-rem-text {
  font-size: 14px; // 不会被转换
  margin-bottom: 12px; // 不会被转换
  color: #374151;
}

.no-rem-button {
  padding: 8px 16px; // 不会被转换
  font-size: 14px; // 不会被转换
  background-color: #f59e0b;
  color: white;
  border: none;
  border-radius: 4px; // 不会被转换
  cursor: pointer;
  
  &:hover {
    background-color: #d97706;
  }
}

// 响应式示例
.responsive-section {
  @include margin-bottom(32);
}

.responsive-title {
  @include font-size(20);
  @include margin-bottom(16);
  color: #666;
}

.responsive-card {
  @include padding(16);
  background-color: #fdf2f8;
  @include border-radius(8);
  border: 1px solid #fce7f3;
}

.responsive-text {
  @include font-size(14);
  color: #374151;
  
  // 响应式字体大小
  @include respond-to('md') {
    @include font-size(16);
  }
  
  @include respond-to('lg') {
    @include font-size(18);
  }
  
  @include respond-to('xl') {
    @include font-size(20);
  }
}

// 信息显示部分
.info-section {
  @include margin-bottom(32);
}

.info-title {
  @include font-size(20);
  @include margin-bottom(16);
  color: #666;
}

.info-card {
  @include padding(16);
  background-color: #f8fafc;
  @include border-radius(8);
  border: 1px solid #e2e8f0;
  
  p {
    @include font-size(14);
    @include margin-bottom(8);
    color: #374151;
    
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}

.update-button {
  @include padding(8, 16);
  @include font-size(14);
  @include margin-top(12);
  background-color: #6366f1;
  color: white;
  border: none;
  @include border-radius(4);
  cursor: pointer;
  
  &:hover {
    background-color: #4f46e5;
  }
}
</style>
