<template>
    <div class="chat-container">
        <div class="chat-with-id-container" :class="rightPanelStore.isRightVisible ? 'px-10px' : 'px-320px'">
            <div class="chat-warp">
                <BubbleList :list="newMessageList" class="bubble-list max-h-[calc(100vh]">
                    <template #content="{ item }">
                        <UserQuestion :question="item.question" v-if="item.isUser" :data="item" />

                        <!-- 系统回答 -->
                        <MessageWrapper v-else :messageId="item.key">
                            <SystemAnswer
                                :components="item.components"
                                :message="item.message"
                                :answerProps="item.answerProps"
                                :data="item"
                            />
                        </MessageWrapper>
                    </template>
                </BubbleList>
                <div class="mb-4">
                    <dialogueInput @sendMessage="sendMessage"></dialogueInput>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 从index.js导入组件
import {
    dialogueInput,
    UserQuestion,
    SystemAnswer,
    MessageWrapper,
    componentMap,
} from '@/components/dialogue/index.js';

import { useSystemResponse } from '@/components/dialogue/hooks/useSystemResponse';
import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler';
// 响应式状态
import { useSessionStore } from '@/components/dialogue/store/modules/session';
import { useUserStore } from '@/components/dialogue/store/modules/user';
import { useRoute } from 'vue-router';
import { computed } from 'vue';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';

// 提供componentMap给子组件
provide('componentMap', componentMap);

// 本地存储消息列表，直接在模板中使用
const localMessageList = ref([]);

const sessionStore = useSessionStore();
const userStore = useUserStore();
const rightPanelStore = useRightPanelStore();
// const { isRightVisible } = storeToRefs(rightPanelStore);

// 使用系统回答钩子
const { cleanupGlobalEvents } = useSystemResponse();

// 使用对话处理钩子
const { sendMessage } = useDialogueHandler();

const route = useRoute();

const supersonicStatusRef = ref('');
const supersonicChatUrlRef = ref('');
const bubbleItems = ref([]);

// 监听路由变化
watch(
    () => route.params?.id,
    async (dialogueId, oldId) => {
        // 更新会话列表
        if (dialogueId) {
            console.log('dialogueId', dialogueId, oldId);
            // 如果本地有发送内容 ，则直接发送
            const v = localStorage.getItem('chatContent');
            if (v) {
                sendMessage(v);
                localStorage.removeItem('chatContent');
            } else {
                try {
                    await sessionStore.requestSessionList();
                    //设置对话ID

                    sessionStore.setDialogueId(dialogueId);

                    // 判断当前会话id是否有聊天记录，有本地缓存则直接赋值展示
                    // if (sessionStore.chatMap && sessionStore.chatMap[`${dialogueId}`]) {
                    //     console.log('使用本地缓存的聊天记录', sessionStore.chatMap[`${dialogueId}`]);
                    //     localMessageList.value = [...sessionStore.chatMap[`${dialogueId}`]];
                    // } else {
                    //     // 无缓存则请求聊天记录
                    //     await sessionStore.requestChatList(`${dialogueId}`);
                    // }
                    await sessionStore.requestChatList(`${dialogueId}`);
                } catch (error) {
                    console.error('处理路由参数变化时出错:', error);
                }
            }
        }
    },
    { deep: true, immediate: true }
);
// 监听sessionStore.messageList的变化，同步到localMessageList
watch(
    () => sessionStore.messageList,
    newList => {
        localMessageList.value = [...newList];
    },
    { deep: true, immediate: true }
);

/**
 * @description 处理消息列表，系统消息最后一个组件添加lastStep标记，所有消息添加placement属性
 */
/**
 * @description 处理消息列表，系统消息最后一个组件添加lastStep标记，所有消息添加placement属性，并判断item是否为最后一个元素
 */
const newMessageList = computed(() => {
    return localMessageList.value.map((item, index, arr) => {
        const placement = item.isUser ? 'end' : 'start';
        const avatar = item.isUser ? userStore.avatar : '';
        let loading = false;
        // 判断当前item是否为最后一个元素
        const isLastItem = index === arr.length - 1;

        // 仅对系统消息且有components数组的情况处理及系统回答
        if (!item.isUser && Array.isArray(item.components) && item.components.length > 0) {
            const components = item.components;
            // 深拷贝最后一个组件并添加lastStep标记
            const newLastComponent = {
                ...components[components.length - 1],
                props: {
                    ...(components[components.length - 1].props || {}),
                    lastStep: true,
                },
            };
            return {
                ...item,
                key: item.id,
                loading,
                maxWidth: '900px',
                isLastItem, // 新增：标记是否为最后一个消息
                components: [...components.slice(0, -1), newLastComponent],
            };
        }
        // 用户输入属性
        return {
            ...item,
            placement,
            maxWidth: '800px',
        };
    });
});
// 清理事件监听器
onUnmounted(() => {
    cleanupGlobalEvents();
});
</script>

<style lang="scss" scoped>
.chat-container {
    background: #fff;
    flex: 1;
    :deep(.el-bubble-list) {
        .el-bubble-content {
            background-color: #fff;
        }
        .el-bubble {
            padding: 0 12px;
            padding-bottom: 24px;
        }
        .el-typewriter {
            overflow: hidden;
            border-radius: 12px;
        }
    }

    .chat-with-id-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        .chat-warp {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 100%;
            height: calc(100vh - 60px);
            .thinking-chain-warp {
                margin-bottom: 12px;
            }
        }

        .chat-defaul-sender {
            width: 100%;
            margin-bottom: 22px;
        }
    }
}
</style>
